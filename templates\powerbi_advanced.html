<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Power BI Dashboard - Ultium Cells</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='sidebar.css') }}">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@24,400,0,0">
    
    <!-- Power BI JavaScript SDK -->
    <script src="https://cdn.jsdelivr.net/npm/powerbi-client@2.22.1/dist/powerbi.min.js"></script>

    <style>
        .container {
            margin-left: 300px;
            padding: 20px;
            transition: margin-left 0.4s ease;
        }

        .container.sidebar-collapsed {
            margin-left: 115px;
        }

        .dashboard-header {
            background: white;
            padding: 20px;
            border-radius: 10px 10px 0 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e0e0e0;
        }

        .dashboard-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .dashboard-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .control-btn {
            padding: 8px 16px;
            border: 1px solid #007acc;
            background: #007acc;
            color: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .control-btn:hover {
            background: #005a9e;
            border-color: #005a9e;
        }

        .control-btn.secondary {
            background: white;
            color: #007acc;
        }

        .control-btn.secondary:hover {
            background: #f5f5f5;
        }

        .dashboard-container {
            background: white;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            height: calc(100vh - 180px);
            position: relative;
        }

        #powerbi-container {
            width: 100%;
            height: 100%;
            border-radius: 0 0 10px 10px;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e3f2fd;
            border-top: 4px solid #007acc;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-bar {
            background: #f8f9fa;
            padding: 10px 20px;
            border-top: 1px solid #e0e0e0;
            font-size: 12px;
            color: #666;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4caf50;
        }

        .status-dot.loading {
            background: #ff9800;
        }

        .status-dot.error {
            background: #f44336;
        }
    </style>
</head>
<body>
    {% include 'sidebar.html' %}
    
    <div class="container">
        <!-- Dashboard Header with Controls -->
        <div class="dashboard-header">
            <h1 class="dashboard-title">Advanced Power BI Dashboard</h1>
            <div class="dashboard-controls">
                <button class="control-btn" onclick="refreshReport()">
                    <span class="material-symbols-rounded" style="font-size: 16px; vertical-align: middle;">refresh</span>
                    Refresh
                </button>
                <button class="control-btn secondary" onclick="toggleFullscreen()">
                    <span class="material-symbols-rounded" style="font-size: 16px; vertical-align: middle;">fullscreen</span>
                    Fullscreen
                </button>
                <button class="control-btn secondary" onclick="exportToPDF()">
                    <span class="material-symbols-rounded" style="font-size: 16px; vertical-align: middle;">download</span>
                    Export PDF
                </button>
                <button class="control-btn secondary" onclick="openInPowerBI()">
                    <span class="material-symbols-rounded" style="font-size: 16px; vertical-align: middle;">open_in_new</span>
                    Open in Power BI
                </button>
            </div>
        </div>

        <!-- Dashboard Container -->
        <div class="dashboard-container">
            <div id="powerbi-container"></div>
            
            <!-- Loading Overlay -->
            <div class="loading-overlay" id="loadingOverlay">
                <div style="text-align: center;">
                    <div class="loading-spinner"></div>
                    <div style="margin-top: 15px; color: #666;">Loading Power BI Report...</div>
                </div>
            </div>

            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-indicator">
                    <div class="status-dot" id="statusDot"></div>
                    <span id="statusText">Initializing...</span>
                </div>
                <div id="lastUpdated">Last updated: Never</div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='sidebar.js') }}"></script>
    <script>
        // Power BI configuration
        const powerbi = window['powerbi-client'];
        let report = null;
        
        // Report configuration
        const reportConfig = {
            type: 'report',
            tokenType: powerbi.models.TokenType.Embed,
            accessToken: '', // This would need to be generated server-side for production
            embedUrl: 'https://app.powerbi.com/links/CGh0KXGQl-?ctid=1fefcbb5-b9fb-4d9d-a4eb-a69982cdd6fe&pbi_source=linkShare',
            id: '', // Report ID would be extracted from the URL
            permissions: powerbi.models.Permissions.Read,
            settings: {
                filterPaneEnabled: true,
                navContentPaneEnabled: true,
                background: powerbi.models.BackgroundType.Transparent
            }
        };

        // Initialize Power BI report
        function initializePowerBI() {
            const container = document.getElementById('powerbi-container');
            const loadingOverlay = document.getElementById('loadingOverlay');
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');

            try {
                // For public reports, we'll use iframe embedding
                // In production, you'd use the SDK with proper authentication
                container.innerHTML = `
                    <iframe 
                        src="https://app.powerbi.com/links/CGh0KXGQl-?ctid=1fefcbb5-b9fb-4d9d-a4eb-a69982cdd6fe&pbi_source=linkShare"
                        width="100%" 
                        height="100%" 
                        frameborder="0" 
                        allowFullScreen="true">
                    </iframe>
                `;

                // Simulate loading completion
                setTimeout(() => {
                    loadingOverlay.style.display = 'none';
                    statusDot.className = 'status-dot';
                    statusText.textContent = 'Connected';
                    updateLastUpdated();
                }, 3000);

            } catch (error) {
                console.error('Error initializing Power BI:', error);
                statusDot.className = 'status-dot error';
                statusText.textContent = 'Connection Error';
                loadingOverlay.style.display = 'none';
            }
        }

        function refreshReport() {
            const loadingOverlay = document.getElementById('loadingOverlay');
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');

            loadingOverlay.style.display = 'flex';
            statusDot.className = 'status-dot loading';
            statusText.textContent = 'Refreshing...';

            // Simulate refresh
            setTimeout(() => {
                loadingOverlay.style.display = 'none';
                statusDot.className = 'status-dot';
                statusText.textContent = 'Connected';
                updateLastUpdated();
            }, 2000);
        }

        function toggleFullscreen() {
            const container = document.querySelector('.dashboard-container');
            if (!document.fullscreenElement) {
                container.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        function exportToPDF() {
            alert('Export functionality would be implemented with proper Power BI SDK authentication');
        }

        function openInPowerBI() {
            window.open('https://app.powerbi.com/links/CGh0KXGQl-?ctid=1fefcbb5-b9fb-4d9d-a4eb-a69982cdd6fe&pbi_source=linkShare', '_blank');
        }

        function updateLastUpdated() {
            const lastUpdated = document.getElementById('lastUpdated');
            const now = new Date();
            lastUpdated.textContent = `Last updated: ${now.toLocaleTimeString()}`;
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializePowerBI();
        });
    </script>
</body>
</html>
