<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Calendar Test - MAQ Software Style</title>
    <!-- DayPilot Pro CSS and JS -->
    <link type="text/css" rel="stylesheet" href="static/daypilot/calendar_white.css" />    
    <script src="static/daypilot/daypilot-all.min.js"></script>

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .calendar-header {
            background: white;
            padding: 20px;
            border-radius: 10px 10px 0 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e0e0e0;
        }

        .calendar-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .calendar-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .view-toggle {
            display: flex;
            background: #f5f5f5;
            border-radius: 8px;
            padding: 4px;
            gap: 2px;
        }

        .view-btn {
            padding: 8px 16px;
            border: none;
            background: transparent;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.2s ease;
        }

        .view-btn.active {
            background: #007acc;
            color: white;
            box-shadow: 0 2px 4px rgba(0, 122, 204, 0.3);
        }

        .view-btn:hover:not(.active) {
            background: #e0e0e0;
            color: #333;
        }

        .nav-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            color: #666;
            transition: all 0.2s ease;
        }

        .nav-btn:hover {
            background: #f5f5f5;
            border-color: #007acc;
            color: #007acc;
        }

        .date-picker {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            color: #666;
        }

        .calendar-container {
            background: white;
            padding: 20px;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            height: calc(100vh - 180px);
            position: relative;
        }

        .calendar-view {
            height: 100%;
            display: none;
        }

        .calendar-view.active {
            display: block;
        }

        #dpMonth, #dpWeek, #dpDay {
            height: 100%;
        }

        /* List View Styles */
        .list-view {
            height: 100%;
            overflow-y: auto;
        }

        .list-item {
            padding: 15px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            gap: 15px;
            transition: background-color 0.2s ease;
        }

        .list-item:hover {
            background-color: #f8f9fa;
        }

        .event-indicator {
            width: 4px;
            height: 40px;
            border-radius: 2px;
            flex-shrink: 0;
        }

        .event-details {
            flex: 1;
        }

        .event-title {
            font-weight: 600;
            font-size: 16px;
            color: #333;
            margin-bottom: 4px;
        }

        .event-meta {
            font-size: 14px;
            color: #666;
            display: flex;
            gap: 20px;
        }

        /* Multi-Month View Styles */
        .multi-month-view {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            height: 100%;
            overflow-y: auto;
            padding: 10px;
        }

        .mini-month {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }

        .mini-month-header {
            background: #f8f9fa;
            padding: 10px;
            text-align: center;
            font-weight: 600;
            color: #333;
            border-bottom: 1px solid #e0e0e0;
        }

        .mini-month-calendar {
            height: 250px;
        }

        .demo-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }

        .demo-info h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }

        .demo-info p {
            margin: 0;
            color: #424242;
        }
    </style>
</head>
<body>
    <div class="demo-info">
        <h3>Enhanced Calendar - MAQ Software Style</h3>
        <p>This calendar features multiple view modes: Month, Week, Day, List, and Multi-Month views with professional styling similar to MAQ Software's Power BI calendar visual.</p>
    </div>

    <!-- Calendar Header with Controls -->
    <div class="calendar-header">
        <h1 class="calendar-title">Calendar</h1>
        <div class="calendar-controls">
            <!-- View Toggle Buttons -->
            <div class="view-toggle">
                <button class="view-btn active" data-view="month">Month</button>
                <button class="view-btn" data-view="week">Week</button>
                <button class="view-btn" data-view="day">Day</button>
                <button class="view-btn" data-view="list">List</button>
                <button class="view-btn" data-view="multi-month">Multi-Month</button>
            </div>
            
            <!-- Navigation Controls -->
            <div class="nav-controls">
                <button class="nav-btn" id="prevBtn">‹ Previous</button>
                <input type="date" class="date-picker" id="datePicker">
                <button class="nav-btn" id="nextBtn">Next ›</button>
                <button class="nav-btn" id="todayBtn">Today</button>
            </div>
        </div>
    </div>

    <!-- Calendar Container -->
    <div class="calendar-container">
        <!-- Month View -->
        <div id="monthView" class="calendar-view active">
            <div id="dpMonth"></div>
        </div>

        <!-- Week View -->
        <div id="weekView" class="calendar-view">
            <div id="dpWeek"></div>
        </div>

        <!-- Day View -->
        <div id="dayView" class="calendar-view">
            <div id="dpDay"></div>
        </div>

        <!-- List View -->
        <div id="listView" class="calendar-view">
            <div class="list-view" id="eventList">
                <!-- Events will be populated here -->
            </div>
        </div>

        <!-- Multi-Month View -->
        <div id="multiMonthView" class="calendar-view">
            <div class="multi-month-view" id="multiMonthContainer">
                <!-- Multiple month calendars will be populated here -->
            </div>
        </div>
    </div>

    <script src="test_calendar.js"></script>
</body>
</html>
