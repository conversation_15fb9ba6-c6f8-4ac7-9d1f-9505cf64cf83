document.addEventListener('DOMContentLoaded', function() {
    // Global variables
    let currentView = 'month';
    let currentDate = new DayPilot.Date();
    let eventsData = [];
    
    // Calendar instances
    let dpMonth, dpWeek, dpDay;
    
    // Sample data for testing
    const sampleEvents = [
        {
            id: 'WO001',
            text: 'Equipment Maintenance',
            start: new DayPilot.Date().addDays(1),
            end: new DayPilot.Date().addDays(1).addHours(2),
            status: 'pending',
            assigned_to: '<PERSON>',
            type: 'maintenance'
        },
        {
            id: 'WO002',
            text: 'Safety Inspection',
            start: new DayPilot.Date().addDays(3),
            end: new DayPilot.Date().addDays(3).addHours(1),
            status: 'inprogress',
            assigned_to: '<PERSON>',
            type: 'inspection'
        },
        {
            id: 'WO003',
            text: 'System Upgrade',
            start: new DayPilot.Date().addDays(-2),
            end: new DayPilot.Date().addDays(-2).addHours(4),
            status: 'completed',
            assigned_to: '<PERSON>',
            type: 'upgrade'
        },
        {
            id: 'WO004',
            text: 'Training Session',
            start: new DayPilot.Date().addDays(7),
            end: new DayPilot.Date().addDays(7).addHours(3),
            status: 'pending',
            assigned_to: 'Sarah Wilson',
            type: 'training'
        }
    ];
    
    // Initialize everything
    eventsData = sampleEvents;
    initializeCalendars();
    setupEventListeners();
    
    function initializeCalendars() {
        // Month View Calendar
        dpMonth = new DayPilot.Month("dpMonth", {
            startDate: currentDate,
            headerDateFormat: "MMMM yyyy",
            dayHeaderHeight: 30,
            cellHeight: 100,
            eventHeight: 25,
            timeRangeSelectedHandling: "Enabled",
            onTimeRangeSelected: handleTimeRangeSelected,
            onEventClick: handleEventClick,
            onBeforeEventRender: handleBeforeEventRender,
            onBeforeCellRender: function(args) {
                if (args.cell.isToday) {
                    args.cell.backColor = "#fff3cd";
                    args.cell.borderColor = "#ffc107";
                }
            }
        });
        
        // Week View Calendar
        dpWeek = new DayPilot.Calendar("dpWeek", {
            viewType: "Week",
            startDate: currentDate,
            headerDateFormat: "MMMM yyyy",
            businessBeginsHour: 6,
            businessEndsHour: 18,
            timeRangeSelectedHandling: "Enabled",
            onTimeRangeSelected: handleTimeRangeSelected,
            onEventClick: handleEventClick,
            onBeforeEventRender: handleBeforeEventRender
        });
        
        // Day View Calendar
        dpDay = new DayPilot.Calendar("dpDay", {
            viewType: "Day",
            startDate: currentDate,
            headerDateFormat: "dddd, MMMM d, yyyy",
            businessBeginsHour: 6,
            businessEndsHour: 18,
            timeRangeSelectedHandling: "Enabled",
            onTimeRangeSelected: handleTimeRangeSelected,
            onEventClick: handleEventClick,
            onBeforeEventRender: handleBeforeEventRender
        });
        
        // Set events and initialize
        dpMonth.events.list = eventsData;
        dpWeek.events.list = eventsData;
        dpDay.events.list = eventsData;
        
        dpMonth.init();
        dpWeek.init();
        dpDay.init();
    }
    
    function setupEventListeners() {
        // View toggle buttons
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const view = this.getAttribute('data-view');
                switchView(view);
            });
        });
        
        // Navigation buttons
        document.getElementById('prevBtn').addEventListener('click', navigatePrevious);
        document.getElementById('nextBtn').addEventListener('click', navigateNext);
        document.getElementById('todayBtn').addEventListener('click', navigateToday);
        
        // Date picker
        const datePicker = document.getElementById('datePicker');
        datePicker.addEventListener('change', function() {
            currentDate = new DayPilot.Date(this.value);
            updateAllCalendars();
        });
        
        // Set initial date picker value
        datePicker.value = currentDate.toString("yyyy-MM-dd");
    }
    
    function switchView(view) {
        // Update active button
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-view="${view}"]`).classList.add('active');
        
        // Hide all views
        document.querySelectorAll('.calendar-view').forEach(viewEl => {
            viewEl.classList.remove('active');
        });
        
        // Show selected view
        currentView = view;
        const viewElement = document.getElementById(`${view}View`);
        if (viewElement) {
            viewElement.classList.add('active');
        }
        
        // Handle specific view logic
        switch(view) {
            case 'month':
                dpMonth.update();
                break;
            case 'week':
                dpWeek.update();
                break;
            case 'day':
                dpDay.update();
                break;
            case 'list':
                renderListView();
                break;
            case 'multi-month':
                renderMultiMonthView();
                break;
        }
    }
    
    function navigatePrevious() {
        switch(currentView) {
            case 'month':
                currentDate = currentDate.addMonths(-1);
                break;
            case 'week':
                currentDate = currentDate.addDays(-7);
                break;
            case 'day':
                currentDate = currentDate.addDays(-1);
                break;
            case 'multi-month':
                currentDate = currentDate.addMonths(-3);
                break;
        }
        updateAllCalendars();
        updateDatePicker();
    }
    
    function navigateNext() {
        switch(currentView) {
            case 'month':
                currentDate = currentDate.addMonths(1);
                break;
            case 'week':
                currentDate = currentDate.addDays(7);
                break;
            case 'day':
                currentDate = currentDate.addDays(1);
                break;
            case 'multi-month':
                currentDate = currentDate.addMonths(3);
                break;
        }
        updateAllCalendars();
        updateDatePicker();
    }
    
    function navigateToday() {
        currentDate = new DayPilot.Date();
        updateAllCalendars();
        updateDatePicker();
    }
    
    function updateDatePicker() {
        document.getElementById('datePicker').value = currentDate.toString("yyyy-MM-dd");
    }
    
    function updateAllCalendars() {
        dpMonth.startDate = currentDate;
        dpWeek.startDate = currentDate;
        dpDay.startDate = currentDate;
        
        dpMonth.update();
        dpWeek.update();
        dpDay.update();
        
        if (currentView === 'list') {
            renderListView();
        } else if (currentView === 'multi-month') {
            renderMultiMonthView();
        }
    }
    
    function handleTimeRangeSelected(args) {
        alert('Time range selected: ' + args.start + ' to ' + args.end);
        // Clear selection
        if (dpMonth) dpMonth.clearSelection();
        if (dpWeek) dpWeek.clearSelection();
        if (dpDay) dpDay.clearSelection();
    }
    
    function handleEventClick(args) {
        alert('Event clicked: ' + args.e.text());
    }
    
    function handleBeforeEventRender(args) {
        const status = args.data.status?.toLowerCase() || 'pending';
        args.data.backColor = getStatusColor(status);
        args.data.barHidden = true;
        args.data.html = `
            <div style="padding: 2px 4px;">
                <div style="font-weight: bold; font-size: 12px;">${args.data.text}</div>
                <div style="font-size: 10px; opacity: 0.8;">WO: ${args.data.id}</div>
                <div style="font-size: 10px; opacity: 0.8;">Assigned: ${args.data.assigned_to || 'Unassigned'}</div>
            </div>
        `;
    }
    
    function renderListView() {
        const listContainer = document.getElementById('eventList');
        listContainer.innerHTML = '';
        
        if (eventsData.length === 0) {
            listContainer.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;">No events to display</div>';
            return;
        }
        
        const sortedEvents = [...eventsData].sort((a, b) => {
            return new Date(a.start) - new Date(b.start);
        });
        
        sortedEvents.forEach(event => {
            const listItem = document.createElement('div');
            listItem.className = 'list-item';
            
            const startDate = new DayPilot.Date(event.start);
            const endDate = new DayPilot.Date(event.end);
            
            listItem.innerHTML = `
                <div class="event-indicator" style="background-color: ${getStatusColor(event.status?.toLowerCase() || 'pending')}"></div>
                <div class="event-details">
                    <div class="event-title">${event.text}</div>
                    <div class="event-meta">
                        <span class="event-time">${startDate.toString("MMM d, yyyy h:mm tt")} - ${endDate.toString("h:mm tt")}</span>
                        <span>WO: ${event.id}</span>
                        <span>Assigned: ${event.assigned_to || 'Unassigned'}</span>
                        <span>Status: ${event.status || 'Pending'}</span>
                    </div>
                </div>
            `;
            
            listContainer.appendChild(listItem);
        });
    }
    
    function renderMultiMonthView() {
        const container = document.getElementById('multiMonthContainer');
        container.innerHTML = '';
        
        for (let i = 0; i < 6; i++) {
            const monthDate = currentDate.addMonths(i);
            const miniMonth = document.createElement('div');
            miniMonth.className = 'mini-month';
            
            const monthId = `miniMonth${i}`;
            miniMonth.innerHTML = `
                <div class="mini-month-header">${monthDate.toString("MMMM yyyy")}</div>
                <div class="mini-month-calendar" id="${monthId}"></div>
            `;
            
            container.appendChild(miniMonth);
            
            setTimeout(() => {
                const miniCal = new DayPilot.Month(monthId, {
                    startDate: monthDate,
                    headerDateFormat: "MMMM yyyy",
                    dayHeaderHeight: 20,
                    cellHeight: 30,
                    eventHeight: 15,
                    showHeader: false,
                    onBeforeEventRender: function(args) {
                        args.data.backColor = getStatusColor(args.data.status?.toLowerCase() || 'pending');
                        args.data.html = args.data.text;
                    },
                    onEventClick: handleEventClick
                });
                
                miniCal.events.list = eventsData;
                miniCal.init();
            }, 100);
        }
    }
    
    function getStatusColor(status) {
        const colors = {
            'completed': '#4CAF50',
            'inprogress': '#2196F3',
            'pending': '#FFC107'
        };
        return colors[status] || colors.pending;
    }
});
