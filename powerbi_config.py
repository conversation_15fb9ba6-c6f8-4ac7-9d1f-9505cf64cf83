# Power BI Configuration
# For production use with private reports

import os
from datetime import datetime, timedelta
import requests
import json

class PowerBIConfig:
    """Configuration class for Power BI embedding"""
    
    def __init__(self):
        # Azure AD App Registration details (set these in environment variables)
        self.CLIENT_ID = os.getenv('POWERBI_CLIENT_ID', '')
        self.CLIENT_SECRET = os.getenv('POWERBI_CLIENT_SECRET', '')
        self.TENANT_ID = os.getenv('POWERBI_TENANT_ID', '1fefcbb5-b9fb-4d9d-a4eb-a69982cdd6fe')
        
        # Power BI Service details
        self.WORKSPACE_ID = os.getenv('POWERBI_WORKSPACE_ID', '')
        self.REPORT_ID = os.getenv('POWERBI_REPORT_ID', '')
        
        # Public report URL (for current implementation)
        self.PUBLIC_REPORT_URL = 'https://app.powerbi.com/links/CGh0KXGQl-?ctid=1fefcbb5-b9fb-4d9d-a4eb-a69982cdd6fe&pbi_source=linkShare'
        
        # API endpoints
        self.AUTHORITY_URL = f'https://login.microsoftonline.com/{self.TENANT_ID}'
        self.SCOPE = ['https://analysis.windows.net/powerbi/api/.default']
        self.POWERBI_API_URL = 'https://api.powerbi.com/v1.0/myorg'

    def get_access_token(self):
        """
        Get access token for Power BI API
        This requires proper Azure AD app registration
        """
        if not self.CLIENT_ID or not self.CLIENT_SECRET:
            return None
            
        token_url = f'{self.AUTHORITY_URL}/oauth2/v2.0/token'
        
        data = {
            'grant_type': 'client_credentials',
            'client_id': self.CLIENT_ID,
            'client_secret': self.CLIENT_SECRET,
            'scope': ' '.join(self.SCOPE)
        }
        
        try:
            response = requests.post(token_url, data=data)
            response.raise_for_status()
            return response.json().get('access_token')
        except Exception as e:
            print(f"Error getting access token: {e}")
            return None

    def get_embed_token(self, report_id, workspace_id):
        """
        Generate embed token for a specific report
        """
        access_token = self.get_access_token()
        if not access_token:
            return None
            
        embed_url = f'{self.POWERBI_API_URL}/groups/{workspace_id}/reports/{report_id}/GenerateToken'
        
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        # Token request body
        body = {
            'accessLevel': 'View',
            'allowSaveAs': False
        }
        
        try:
            response = requests.post(embed_url, headers=headers, json=body)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"Error generating embed token: {e}")
            return None

    def get_report_embed_url(self, report_id, workspace_id):
        """
        Get the embed URL for a specific report
        """
        return f'https://app.powerbi.com/reportEmbed?reportId={report_id}&groupId={workspace_id}'

    def get_embed_config(self):
        """
        Get complete embed configuration
        """
        if not self.WORKSPACE_ID or not self.REPORT_ID:
            # Return public report configuration
            return {
                'type': 'public',
                'embedUrl': self.PUBLIC_REPORT_URL,
                'reportId': None,
                'accessToken': None
            }
        
        # Generate embed token for private report
        embed_token_response = self.get_embed_token(self.REPORT_ID, self.WORKSPACE_ID)
        
        if embed_token_response:
            return {
                'type': 'private',
                'embedUrl': self.get_report_embed_url(self.REPORT_ID, self.WORKSPACE_ID),
                'reportId': self.REPORT_ID,
                'accessToken': embed_token_response.get('token'),
                'expiration': embed_token_response.get('expiration')
            }
        else:
            # Fallback to public report
            return {
                'type': 'public',
                'embedUrl': self.PUBLIC_REPORT_URL,
                'reportId': None,
                'accessToken': None
            }

# Environment variables setup instructions
SETUP_INSTRUCTIONS = """
To set up Power BI embedding with private reports:

1. Register an Azure AD application:
   - Go to Azure Portal > Azure Active Directory > App registrations
   - Create a new registration
   - Note the Application (client) ID and Directory (tenant) ID

2. Create a client secret:
   - In your app registration, go to Certificates & secrets
   - Create a new client secret
   - Note the secret value

3. Configure Power BI permissions:
   - In your app registration, go to API permissions
   - Add Power BI Service permissions
   - Grant admin consent

4. Set environment variables:
   export POWERBI_CLIENT_ID="your-client-id"
   export POWERBI_CLIENT_SECRET="your-client-secret"
   export POWERBI_TENANT_ID="your-tenant-id"
   export POWERBI_WORKSPACE_ID="your-workspace-id"
   export POWERBI_REPORT_ID="your-report-id"

5. Enable Power BI service principal:
   - Go to Power BI Admin Portal
   - Enable "Allow service principals to use Power BI APIs"
   - Add your app to the security group if required
"""

if __name__ == "__main__":
    print(SETUP_INSTRUCTIONS)
