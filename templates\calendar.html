<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calendar - Ultium Cells</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='sidebar.css') }}">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@24,400,0,0">
    <!-- DayPilot Pro CSS and JS -->
    <link type="text/css" rel="stylesheet" href="{{ url_for('static', filename='daypilot/calendar_white.css') }}" />    
    <script src="{{ url_for('static', filename='daypilot/daypilot-all.min.js') }}"></script>

    <style>
        .container {
            margin-left: 300px;
            padding: 20px;
            transition: margin-left 0.4s ease;
        }

        .container.sidebar-collapsed {
            margin-left: 115px;
        }

        .calendar-header {
            background: white;
            padding: 20px;
            border-radius: 10px 10px 0 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e0e0e0;
        }

        .calendar-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .calendar-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .view-toggle {
            display: flex;
            background: #f5f5f5;
            border-radius: 8px;
            padding: 4px;
            gap: 2px;
        }

        .view-btn {
            padding: 8px 16px;
            border: none;
            background: transparent;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.2s ease;
        }

        .view-btn.active {
            background: #007acc;
            color: white;
            box-shadow: 0 2px 4px rgba(0, 122, 204, 0.3);
        }

        .view-btn:hover:not(.active) {
            background: #e0e0e0;
            color: #333;
        }

        .nav-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            color: #666;
            transition: all 0.2s ease;
        }

        .nav-btn:hover {
            background: #f5f5f5;
            border-color: #007acc;
            color: #007acc;
        }

        .date-picker {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            color: #666;
        }

        .calendar-container {
            background: white;
            padding: 20px;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            height: calc(100vh - 180px);
            position: relative;
        }

        .calendar-view {
            height: 100%;
            display: none;
        }

        .calendar-view.active {
            display: block;
        }

        #dp, #dpMonth, #dpWeek, #dpDay {
            height: 100%;
        }

        /* List View Styles */
        .list-view {
            height: 100%;
            overflow-y: auto;
        }

        .list-item {
            padding: 15px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            gap: 15px;
            transition: background-color 0.2s ease;
        }

        .list-item:hover {
            background-color: #f8f9fa;
        }

        .list-item:last-child {
            border-bottom: none;
        }

        .event-indicator {
            width: 4px;
            height: 40px;
            border-radius: 2px;
            flex-shrink: 0;
        }

        .event-details {
            flex: 1;
        }

        .event-title {
            font-weight: 600;
            font-size: 16px;
            color: #333;
            margin-bottom: 4px;
        }

        .event-meta {
            font-size: 14px;
            color: #666;
            display: flex;
            gap: 20px;
        }

        .event-time {
            font-weight: 500;
        }

        /* Status-based colors */
        .status-completed {
            background-color: #4CAF50 !important;
        }

        .status-inprogress {
            background-color: #2196F3 !important;
        }

        .status-pending {
            background-color: #FFC107 !important;
        }

        /* Hide DayPilot orange selection */
        div[unselectable="on"][style*="background-color: rgb(255, 102, 0)"] {
            display: none !important;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .calendar-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .calendar-controls {
                justify-content: space-between;
            }

            .view-toggle {
                flex: 1;
            }

            .view-btn {
                flex: 1;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    {% include 'sidebar.html' %}
    
    <div class="container">
        <!-- Calendar Header with Controls -->
        <div class="calendar-header">
            <h1 class="calendar-title">Calendar</h1>
            <div class="calendar-controls">
                <!-- View Toggle Buttons -->
                <div class="view-toggle">
                    <button class="view-btn active" data-view="month">Month</button>
                    <button class="view-btn" data-view="week">Week</button>
                    <button class="view-btn" data-view="day">Day</button>
                    <button class="view-btn" data-view="list">List</button>
                </div>

                <!-- Navigation Controls -->
                <div class="nav-controls">
                    <button class="nav-btn" id="prevBtn">‹ Previous</button>
                    <input type="date" class="date-picker" id="datePicker">
                    <button class="nav-btn" id="nextBtn">Next ›</button>
                    <button class="nav-btn" id="todayBtn">Today</button>
                </div>
            </div>
        </div>

        <!-- Calendar Container -->
        <div class="calendar-container">
            <!-- Month View -->
            <div id="monthView" class="calendar-view active">
                <div id="dpMonth"></div>
            </div>

            <!-- Week View -->
            <div id="weekView" class="calendar-view">
                <div id="dpWeek"></div>
            </div>

            <!-- Day View -->
            <div id="dayView" class="calendar-view">
                <div id="dpDay"></div>
            </div>

            <!-- List View -->
            <div id="listView" class="calendar-view">
                <div class="list-view" id="eventList">
                    <!-- Events will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='sidebar.js') }}"></script>
    <script src="{{ url_for('static', filename='calendar.js') }}"></script>
</body>
</html>



