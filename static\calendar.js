document.addEventListener('DOMContentLoaded', function() {
    // Global variables
    let currentView = 'month';
    let currentDate = new DayPilot.Date();
    let eventsData = [];

    // Calendar instances
    let dpMonth, dpWeek, dpDay;

    // Initialize all calendar views
    initializeCalendars();

    // Setup event listeners
    setupEventListeners();

    // Load events
    loadEvents();

    function initializeCalendars() {
        // Month View Calendar
        dpMonth = new DayPilot.Month("dpMonth", {
            startDate: currentDate,
            headerDateFormat: "MMMM yyyy",
            dayHeaderHeight: 30,
            cellHeight: 100,
            eventHeight: 25,
            timeRangeSelectedHandling: "Enabled",
            onTimeRangeSelected: handleTimeRangeSelected,
            onEventClick: handleEventClick,
            onBeforeEventRender: handleBeforeEventRender,
            onBeforeCellRender: function(args) {
                // Add subtle styling to cells
                if (args.cell.isToday) {
                    args.cell.backColor = "#fff3cd";
                    args.cell.borderColor = "#ffc107";
                }
            }
        });

        // Week View Calendar
        dpWeek = new DayPilot.Calendar("dpWeek", {
            viewType: "Week",
            startDate: currentDate,
            headerDateFormat: "MMMM yyyy",
            businessBeginsHour: 6,
            businessEndsHour: 18,
            timeRangeSelectedHandling: "Enabled",
            onTimeRangeSelected: handleTimeRangeSelected,
            onEventClick: handleEventClick,
            onBeforeEventRender: handleBeforeEventRender
        });

        // Day View Calendar
        dpDay = new DayPilot.Calendar("dpDay", {
            viewType: "Day",
            startDate: currentDate,
            headerDateFormat: "dddd, MMMM d, yyyy",
            businessBeginsHour: 6,
            businessEndsHour: 18,
            timeRangeSelectedHandling: "Enabled",
            onTimeRangeSelected: handleTimeRangeSelected,
            onEventClick: handleEventClick,
            onBeforeEventRender: handleBeforeEventRender
        });

        // Initialize all calendars
        dpMonth.init();
        dpWeek.init();
        dpDay.init();
    }

    function setupEventListeners() {
        // View toggle buttons
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const view = this.getAttribute('data-view');
                switchView(view);
            });
        });

        // Navigation buttons
        document.getElementById('prevBtn').addEventListener('click', navigatePrevious);
        document.getElementById('nextBtn').addEventListener('click', navigateNext);
        document.getElementById('todayBtn').addEventListener('click', navigateToday);

        // Date picker
        const datePicker = document.getElementById('datePicker');
        datePicker.addEventListener('change', function() {
            currentDate = new DayPilot.Date(this.value);
            updateAllCalendars();
        });

        // Set initial date picker value
        datePicker.value = currentDate.toString("yyyy-MM-dd");
    }

    function switchView(view) {
        // Update active button
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-view="${view}"]`).classList.add('active');

        // Hide all views
        document.querySelectorAll('.calendar-view').forEach(viewEl => {
            viewEl.classList.remove('active');
        });

        // Show selected view
        currentView = view;
        const viewElement = document.getElementById(`${view}View`);
        if (viewElement) {
            viewElement.classList.add('active');
        }

        // Handle specific view logic
        switch(view) {
            case 'month':
                dpMonth.update();
                break;
            case 'week':
                dpWeek.update();
                break;
            case 'day':
                dpDay.update();
                break;
            case 'list':
                renderListView();
                break;
        }
    }

    function navigatePrevious() {
        switch(currentView) {
            case 'month':
                currentDate = currentDate.addMonths(-1);
                break;
            case 'week':
                currentDate = currentDate.addDays(-7);
                break;
            case 'day':
                currentDate = currentDate.addDays(-1);
                break;
        }
        updateAllCalendars();
        updateDatePicker();
    }

    function navigateNext() {
        switch(currentView) {
            case 'month':
                currentDate = currentDate.addMonths(1);
                break;
            case 'week':
                currentDate = currentDate.addDays(7);
                break;
            case 'day':
                currentDate = currentDate.addDays(1);
                break;
        }
        updateAllCalendars();
        updateDatePicker();
    }

    function navigateToday() {
        currentDate = new DayPilot.Date();
        updateAllCalendars();
        updateDatePicker();
    }

    function updateDatePicker() {
        document.getElementById('datePicker').value = currentDate.toString("yyyy-MM-dd");
    }

    function updateAllCalendars() {
        dpMonth.startDate = currentDate;
        dpWeek.startDate = currentDate;
        dpDay.startDate = currentDate;

        dpMonth.update();
        dpWeek.update();
        dpDay.update();

        if (currentView === 'list') {
            renderListView();
        }
    }

    function handleTimeRangeSelected(args) {
        // Handle time range selection for creating new events
        const modal = new DayPilot.Modal({
            onClosed: function(args) {
                if (args.result) {
                    // Add new event to all calendars
                    dpMonth.events.add(args.result);
                    dpWeek.events.add(args.result);
                    dpDay.events.add(args.result);
                    eventsData.push(args.result);
                }
                // Clear selection on all calendars
                dpMonth.clearSelection();
                dpWeek.clearSelection();
                dpDay.clearSelection();
            }
        });
    }

    function handleEventClick(args) {
        // Handle event click for editing
        const modal = new DayPilot.Modal({
            onClosed: function(args) {
                if (args.result) {
                    // Update event in all calendars
                    dpMonth.events.update(args.result);
                    dpWeek.events.update(args.result);
                    dpDay.events.update(args.result);

                    // Update in eventsData array
                    const index = eventsData.findIndex(e => e.id === args.result.id);
                    if (index !== -1) {
                        eventsData[index] = args.result;
                    }
                }
            }
        });
    }

    function handleBeforeEventRender(args) {
        // Customize event appearance based on status
        const status = args.data.status?.toLowerCase() || 'pending';
        args.data.backColor = getStatusColor(status);
        args.data.barHidden = true;
        args.data.html = `
            <div style="padding: 2px 4px;">
                <div style="font-weight: bold; font-size: 12px;">${args.data.text}</div>
                <div style="font-size: 10px; opacity: 0.8;">WO: ${args.data.id}</div>
                <div style="font-size: 10px; opacity: 0.8;">Assigned: ${args.data.assigned_to || 'Unassigned'}</div>
            </div>
        `;
    }

    function renderListView() {
        const listContainer = document.getElementById('eventList');
        listContainer.innerHTML = '';

        if (eventsData.length === 0) {
            listContainer.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;">No events to display</div>';
            return;
        }

        // Sort events by start date
        const sortedEvents = [...eventsData].sort((a, b) => {
            return new Date(a.start) - new Date(b.start);
        });

        sortedEvents.forEach(event => {
            const listItem = document.createElement('div');
            listItem.className = 'list-item';

            const startDate = new DayPilot.Date(event.start);
            const endDate = new DayPilot.Date(event.end);

            listItem.innerHTML = `
                <div class="event-indicator" style="background-color: ${getStatusColor(event.status?.toLowerCase() || 'pending')}"></div>
                <div class="event-details">
                    <div class="event-title">${event.text}</div>
                    <div class="event-meta">
                        <span class="event-time">${startDate.toString("MMM d, yyyy h:mm tt")} - ${endDate.toString("h:mm tt")}</span>
                        <span>WO: ${event.id}</span>
                        <span>Assigned: ${event.assigned_to || 'Unassigned'}</span>
                        <span>Status: ${event.status || 'Pending'}</span>
                    </div>
                </div>
            `;

            listContainer.appendChild(listItem);
        });
    }

    function loadEvents() {
        fetch('/api/events')
            .then(response => response.json())
            .then(data => {
                eventsData = data.map(wo => ({
                    id: wo.id,
                    text: wo.title,
                    start: new DayPilot.Date(wo.start),
                    end: new DayPilot.Date(wo.end),
                    status: wo.status,
                    assigned_to: wo.assigned_to,
                    type: wo.type
                }));

                // Update all calendar views with events
                dpMonth.events.list = eventsData;
                dpWeek.events.list = eventsData;
                dpDay.events.list = eventsData;

                dpMonth.update();
                dpWeek.update();
                dpDay.update();
            })
            .catch(error => {
                console.error('Error loading work orders:', error);
                document.getElementById('eventList').innerHTML =
                    '<div style="text-align: center; padding: 40px; color: #d32f2f;">Error loading events. Please try again.</div>';
            });
    }

    function getStatusColor(status) {
        const colors = {
            'completed': '#4CAF50',
            'inprogress': '#2196F3',
            'pending': '#FFC107'
        };
        return colors[status] || colors.pending;
    }
});

